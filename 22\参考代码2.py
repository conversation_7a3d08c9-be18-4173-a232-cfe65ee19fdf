提取节点力
# -*- coding: mbcs -*-
from part import *
from material import *
from section import *
from assembly import *
from step import *
from interaction import *
from load import *
from mesh import *
from optimization import *
from job import *
from sketch import *
from visualization import *
from connectorBehavior import *
import math
import os
import numpy as np
import xyPlot


#odb_path = 'D:/australia2024/TPMS/tpms-matlabcode/HJPminisurf/test/main1.odb'
odb_path = os.path.abspath(__doc__)+'/' + 'main1.odb'
myodb = openOdb(path=odb_path)
lastframe=myodb.steps['Step-1'].frames[-1]
rf1=lastframe.fieldOutputs['NFORC1']
rf2=lastframe.fieldOutputs['NFORC2']
rf3=lastframe.fieldOutputs['NFORC3']
file_path=odb_path = os.path.abspath(__doc__)+'/' + 'pythonset.txt'
file = open(file_path, 'r')
infos = file.readlines()
file.close()
setid = [int(line.strip()) for line in infos]
setRF=[]
for tmpid in setid:
    tmpregion = myodb.rootAssembly.nodeSets['B-'+str(tmpid)]
    setrf1 = rf1.getSubset(region=tmpregion).values
    setrf1 = [item.data for item in setrf1]
    setrf1=sum(setrf1)
    setrf2 = rf2.getSubset(region=tmpregion).values
    setrf2 = [item.data for item in setrf2]
    setrf2=sum(setrf2)
    setrf3 = rf3.getSubset(region=tmpregion).values
    setrf3 = [item.data for item in setrf3]
    setrf3=sum(setrf3)
    setRF.append([tmpid,setrf1,setrf2,setrf3])
# resultfile = open(os.path.abspath(__doc__)+'/'+'result1.txt','w')
# np.savetxt(resultfile, np.array(setRF), fmt='%d %.9f %.9f %.9f', delimiter=' ')
file=os.path.abspath(__doc__)+'/'+'result1.txt'
with open(file, 'w') as f:
    for row in setRF:
        row_str = ' '.join(map(str, row))
        f.write(row_str + '\n')
f.close()
myodb.close()
print('finish')
# print(np.array(setRF))