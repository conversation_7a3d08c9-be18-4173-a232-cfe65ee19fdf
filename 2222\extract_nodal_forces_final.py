# -*- coding: mbcs -*-
"""
Abaqus节点力提取工具 - 最终版本
功能：
1. 提取SET-RESPON中所有节点的真实节点力（NFORC1, NFORC2, NFORC3）
2. 获取全部时间步的数据
3. 按节点ID作为前缀保存到Excel文件
4. 生成力-时间图表
5. 包含数据分析和统计功能

重要说明：
- 本脚本提取的是节点的真实等效合力，而不是Abaqus CAE中显示的平均值
- 根据CSDN文章(https://blog.csdn.net/qianxin1234/article/details/140739068)：
  * Abaqus CAE中显示的NFORC是节点在所有共享单元上的平均值
  * 真实的节点力应该是该节点在所有共享单元上的NFORC之和
  * 本脚本通过分别提取NFORC1、NFORC2、NFORC3并对每个节点求和来获得真实力值
- NFORC方向已修正（加负号），符合实际物理意义

使用方法：
1. 在Abaqus CAE中打开包含结果的odb文件
2. 确保Field Output中包含NFORC1、NFORC2、NFORC3
3. 运行此脚本：File -> Run Script -> 选择此文件
4. 脚本会自动提取数据并生成Excel文件和图表

依赖库：pandas, openpyxl, matplotlib, numpy
"""

from abaqus import *
from abaqusConstants import *
from visualization import *
import os
import sys

# 尝试导入所需库
try:
    import pandas as pd
    import numpy as np
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告：未安装pandas/numpy，将使用基础CSV格式")

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
except ImportError:
    HAS_MATPLOTLIB = False
    print("警告：未安装matplotlib，无法生成图表")

# ==================== 配置参数 ====================
NODE_SET_NAME = "SET-RESPON"  # 节点集名称
OUTPUT_DIR = ""  # 留空则使用脚本所在目录

def extract_nodal_forces():
    """提取节点力的主函数"""
    
    print("=" * 60)
    print("Abaqus节点力提取工具 - 最终版本")
    print("=" * 60)
    
    # 获取当前打开的odb文件
    try:
        viewport = session.viewports[session.currentViewportName]
        if hasattr(viewport, 'odbDisplay'):
            odb_name = viewport.odbDisplay.odbName
            myodb = session.odbs[odb_name]
            print("使用当前打开的odb文件: %s" % odb_name)
        else:
            print("错误：请先在Abaqus CAE中打开odb文件")
            return None, None
    except:
        print("错误：无法获取当前odb文件")
        return None, None
    
    # 检查节点集
    try:
        node_set = myodb.rootAssembly.nodeSets[NODE_SET_NAME]
        print("找到节点集: %s" % NODE_SET_NAME)
        
        # 获取节点标签
        node_labels = []
        for instance_nodes in node_set.nodes:
            for node in instance_nodes:
                node_labels.append(node.label)
        
        print("节点数量: %d" % len(node_labels))
        print("节点ID范围: %d - %d" % (min(node_labels), max(node_labels)))
        
    except KeyError:
        print("错误：未找到节点集 %s" % NODE_SET_NAME)
        print("可用的节点集：")
        for set_name in myodb.rootAssembly.nodeSets.keys():
            print("  - %s" % set_name)
        return None, None
    
    # 获取所有步骤
    step_names = myodb.steps.keys()
    print("处理步骤: %s" % ', '.join(step_names))
    
    # 存储所有数据
    all_data = {}
    
    # 初始化每个节点的数据存储
    for node_id in node_labels:
        all_data[node_id] = {
            'Time': [],
            'Fx': [],
            'Fy': [],
            'Fz': []
        }
    
    # 遍历所有步骤和帧
    total_frames = 0
    for step_name in step_names:
        step = myodb.steps[step_name]
        print("处理步骤 %s，包含 %d 个帧..." % (step_name, len(step.frames)))
        
        for frame_idx, frame in enumerate(step.frames):
            try:
                time_val = frame.frameValue
                
                # 关键修改：使用NFORC1, NFORC2, NFORC3分别提取，然后求和
                # 这样可以获得节点的真实等效合力，而不是平均值

                # 检查各个NFORC分量是否存在
                if 'NFORC1' not in frame.fieldOutputs or 'NFORC2' not in frame.fieldOutputs or 'NFORC3' not in frame.fieldOutputs:
                    print("警告：帧 %d 中未找到NFORC1/NFORC2/NFORC3字段" % frame_idx)
                    continue

                # 分别获取三个方向的NFORC
                nforc1_field = frame.fieldOutputs['NFORC1']
                nforc2_field = frame.fieldOutputs['NFORC2']
                nforc3_field = frame.fieldOutputs['NFORC3']

                # 提取SET-RESPON节点的各方向NFORC值
                subset1 = nforc1_field.getSubset(region=node_set)
                subset2 = nforc2_field.getSubset(region=node_set)
                subset3 = nforc3_field.getSubset(region=node_set)

                # 创建当前帧的节点力字典 - 对每个节点的所有单元贡献求和
                current_frame_forces = {}

                # 处理X方向力（NFORC1）
                for value in subset1.values:
                    node_label = value.nodeLabel
                    fx = -value.data  # 加负号修正方向
                    if node_label not in current_frame_forces:
                        current_frame_forces[node_label] = [0.0, 0.0, 0.0]
                    current_frame_forces[node_label][0] += fx

                # 处理Y方向力（NFORC2）
                for value in subset2.values:
                    node_label = value.nodeLabel
                    fy = -value.data  # 加负号修正方向
                    if node_label not in current_frame_forces:
                        current_frame_forces[node_label] = [0.0, 0.0, 0.0]
                    current_frame_forces[node_label][1] += fy

                # 处理Z方向力（NFORC3）
                for value in subset3.values:
                    node_label = value.nodeLabel
                    fz = -value.data  # 加负号修正方向
                    if node_label not in current_frame_forces:
                        current_frame_forces[node_label] = [0.0, 0.0, 0.0]
                    current_frame_forces[node_label][2] += fz
                
                # 将数据添加到所有节点
                for node_id in node_labels:
                    all_data[node_id]['Time'].append(time_val)
                    if node_id in current_frame_forces:
                        fx, fy, fz = current_frame_forces[node_id]
                        all_data[node_id]['Fx'].append(fx)
                        all_data[node_id]['Fy'].append(fy)
                        all_data[node_id]['Fz'].append(fz)
                    else:
                        # 如果某个节点在当前帧没有数据，填入0
                        all_data[node_id]['Fx'].append(0.0)
                        all_data[node_id]['Fy'].append(0.0)
                        all_data[node_id]['Fz'].append(0.0)
                
                total_frames += 1
                if frame_idx % 20 == 0:
                    print("  处理进度: %d/%d 帧" % (frame_idx + 1, len(step.frames)))
                
            except Exception as e:
                print("处理帧 %d 时出错: %s" % (frame_idx, str(e)))
                continue
    
    print("数据提取完成！")
    print("总帧数: %d" % total_frames)
    print("节点数: %d" % len(all_data))
    
    return all_data, myodb.name

def save_data_to_excel(all_data, odb_name):
    """保存数据到Excel文件"""
    
    # 确定输出目录
    if OUTPUT_DIR:
        output_dir = OUTPUT_DIR
    else:
        script_path = os.path.abspath(__file__)
        output_dir = os.path.dirname(script_path)
        if not output_dir:
            output_dir = os.getcwd()
    
    # 创建输出文件名
    base_name = os.path.splitext(os.path.basename(odb_name))[0]
    
    if HAS_PANDAS:
        # 生成Excel文件
        excel_file = os.path.join(output_dir, "%s_SET_RESPON_forces.xlsx" % base_name)
        
        try:
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # 1. 创建汇总统计表
                summary_data = []
                for node_id in sorted(all_data.keys()):
                    node_data = all_data[node_id]
                    if node_data['Fx']:  # 确保有数据
                        fx_array = np.array(node_data['Fx'])
                        fy_array = np.array(node_data['Fy'])
                        fz_array = np.array(node_data['Fz'])
                        f_total = np.sqrt(fx_array**2 + fy_array**2 + fz_array**2)
                        
                        summary_data.append({
                            'NodeID': node_id,
                            'Max_Fx': np.max(np.abs(fx_array)),
                            'Max_Fy': np.max(np.abs(fy_array)),
                            'Max_Fz': np.max(np.abs(fz_array)),
                            'Max_F_Total': np.max(f_total),
                            'Mean_F_Total': np.mean(f_total),
                            'Time_at_Max_F': node_data['Time'][np.argmax(f_total)]
                        })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df = summary_df.sort_values('Max_F_Total', ascending=False)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # 2. 创建所有数据的合并表（按节点ID作为前缀）
                all_records = []
                for node_id in sorted(all_data.keys()):
                    node_data = all_data[node_id]
                    for i in range(len(node_data['Time'])):
                        fx, fy, fz = node_data['Fx'][i], node_data['Fy'][i], node_data['Fz'][i]
                        f_total = (fx**2 + fy**2 + fz**2)**0.5
                        all_records.append({
                            'NodeID': node_id,
                            'Time': node_data['Time'][i],
                            'Fx': fx,
                            'Fy': fy,
                            'Fz': fz,
                            'F_Total': f_total
                        })
                
                all_df = pd.DataFrame(all_records)
                all_df.to_excel(writer, sheet_name='All_Data', index=False)
                
                # 3. 为关键节点（受力最大的前10个）创建单独的工作表
                top_nodes = summary_df.head(10)['NodeID'].tolist()
                for node_id in top_nodes:
                    node_data = all_data[node_id]
                    df = pd.DataFrame(node_data)
                    # 添加合力列
                    df['F_Total'] = np.sqrt(df['Fx']**2 + df['Fy']**2 + df['Fz']**2)
                    sheet_name = "Node_%d" % node_id
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print("Excel文件已保存到: %s" % excel_file)
            return excel_file, summary_df
            
        except Exception as e:
            print("保存Excel文件时出错：%s" % str(e))
            return save_data_to_csv(all_data, base_name, output_dir)
    
    else:
        # 回退到CSV格式
        return save_data_to_csv(all_data, base_name, output_dir)

def save_data_to_csv(all_data, base_name, output_dir):
    """保存数据到CSV文件"""

    csv_file = os.path.join(output_dir, "%s_SET_RESPON_forces.csv" % base_name)

    try:
        with open(csv_file, 'w') as f:
            # 写入表头
            f.write("NodeID,Time,Fx,Fy,Fz,F_Total\n")

            # 写入数据
            for node_id in sorted(all_data.keys()):
                node_data = all_data[node_id]
                for i in range(len(node_data['Time'])):
                    fx, fy, fz = node_data['Fx'][i], node_data['Fy'][i], node_data['Fz'][i]
                    f_total = (fx**2 + fy**2 + fz**2)**0.5
                    f.write("%d,%.6f,%.6e,%.6e,%.6e,%.6e\n" % (
                        node_id, node_data['Time'][i], fx, fy, fz, f_total))

        print("CSV文件已保存到: %s" % csv_file)
        return csv_file, None

    except Exception as e:
        print("保存CSV文件时出错: %s" % str(e))
        return None, None

def generate_force_plots(all_data, summary_df, output_dir, base_name):
    """生成力-时间图表"""

    if not HAS_MATPLOTLIB or not HAS_PANDAS:
        print("跳过图表生成：缺少matplotlib或pandas库")
        return

    print("生成力-时间图表...")

    # 获取受力最大的5个节点
    if summary_df is not None:
        top_nodes = summary_df.head(5)['NodeID'].tolist()
    else:
        # 如果没有summary_df，随机选择前5个节点
        top_nodes = sorted(all_data.keys())[:5]

    # 1. 生成单个节点的详细图（最大受力节点）
    if top_nodes:
        max_force_node = top_nodes[0]
        plot_single_node_forces(all_data[max_force_node], max_force_node,
                               os.path.join(output_dir, "%s_node_%d_forces.png" % (base_name, max_force_node)))

    # 2. 生成多节点对比图
    plot_multiple_nodes_comparison(all_data, top_nodes, 'F_Total',
                                 os.path.join(output_dir, "%s_top_nodes_comparison.png" % base_name))

    # 3. 生成各方向力的对比图
    for component in ['Fx', 'Fy', 'Fz']:
        plot_multiple_nodes_comparison(all_data, top_nodes, component,
                                     os.path.join(output_dir, "%s_top_nodes_%s.png" % (base_name, component)))

def plot_single_node_forces(node_data, node_id, save_path):
    """绘制单个节点的力-时间图"""

    if not node_data['Time']:
        return

    # 转换为numpy数组便于计算
    time = np.array(node_data['Time'])
    fx = np.array(node_data['Fx'])
    fy = np.array(node_data['Fy'])
    fz = np.array(node_data['Fz'])
    f_total = np.sqrt(fx**2 + fy**2 + fz**2)

    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle(u'节点 %d 力-时间历程' % node_id, fontsize=16)

    # 绘制各分量
    axes[0, 0].plot(time, fx, 'b-', linewidth=1.5)
    axes[0, 0].set_title('X方向力 (Fx)')
    axes[0, 0].set_xlabel('时间 (s)')
    axes[0, 0].set_ylabel('力 (N)')
    axes[0, 0].grid(True, alpha=0.3)

    axes[0, 1].plot(time, fy, 'r-', linewidth=1.5)
    axes[0, 1].set_title('Y方向力 (Fy)')
    axes[0, 1].set_xlabel('时间 (s)')
    axes[0, 1].set_ylabel('力 (N)')
    axes[0, 1].grid(True, alpha=0.3)

    axes[1, 0].plot(time, fz, 'g-', linewidth=1.5)
    axes[1, 0].set_title('Z方向力 (Fz)')
    axes[1, 0].set_xlabel('时间 (s)')
    axes[1, 0].set_ylabel('力 (N)')
    axes[1, 0].grid(True, alpha=0.3)

    axes[1, 1].plot(time, f_total, 'm-', linewidth=1.5)
    axes[1, 1].set_title('合力 (F_Total)')
    axes[1, 1].set_xlabel('时间 (s)')
    axes[1, 1].set_ylabel('力 (N)')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print("单节点图表已保存到: %s" % save_path)

def plot_multiple_nodes_comparison(all_data, node_ids, force_component, save_path):
    """绘制多个节点的力对比图"""

    plt.figure(figsize=(12, 8))

    colors = plt.cm.tab10(np.linspace(0, 1, len(node_ids)))

    for i, node_id in enumerate(node_ids):
        if node_id not in all_data:
            continue

        node_data = all_data[node_id]
        if not node_data['Time']:
            continue

        time = np.array(node_data['Time'])

        if force_component == 'F_Total':
            fx = np.array(node_data['Fx'])
            fy = np.array(node_data['Fy'])
            fz = np.array(node_data['Fz'])
            force_values = np.sqrt(fx**2 + fy**2 + fz**2)
        else:
            force_values = np.array(node_data[force_component])

        plt.plot(time, force_values, color=colors[i], linewidth=1.5,
                label=u'节点 %d' % node_id)

    plt.title(u'多节点%s对比' % force_component, fontsize=16)
    plt.xlabel('时间 (s)')
    plt.ylabel('力 (N)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print("对比图表已保存到: %s" % save_path)

def main():
    """主函数"""

    # 提取节点力数据
    all_data, odb_name = extract_nodal_forces()
    if all_data is None:
        return

    # 保存数据到Excel/CSV
    output_file, summary_df = save_data_to_excel(all_data, odb_name)
    if output_file is None:
        print("数据保存失败")
        return

    # 生成图表
    output_dir = os.path.dirname(output_file)
    base_name = os.path.splitext(os.path.basename(odb_name))[0]
    generate_force_plots(all_data, summary_df, output_dir, base_name)

    # 打印完成信息
    print("\n" + "=" * 60)
    print("节点力提取完成！")
    print("输出文件: %s" % output_file)
    if summary_df is not None:
        print("受力最大的5个节点:")
        for i, (_, row) in enumerate(summary_df.head(5).iterrows(), 1):
            print("  %d. 节点 %d: 最大合力 = %.3e N" % (i, row['NodeID'], row['Max_F_Total']))
    print("=" * 60)

# 运行主函数
if __name__ == "__main__":
    main()
