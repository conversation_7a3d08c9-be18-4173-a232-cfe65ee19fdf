Abaqus节点力提取脚本使用说明
================================

脚本文件：extract_nodal_forces_final.py

功能：
- 提取SET-RESPON中所有节点的真实节点力（NFORC1, NFORC2, NFORC3）
- 获取全部时间步的数据
- 按节点ID保存到Excel文件
- 包含数据统计和分析功能

使用步骤：
=========

1. 准备工作
   - 确保Abaqus分析已完成，生成了odb文件
   - 确保Field Output中包含NFORC1、NFORC2、NFORC3字段
   - 确保模型中存在名为"SET-RESPON"的节点集

2. 运行脚本
   - 打开Abaqus CAE
   - 打开包含结果的odb文件
   - 在Abaqus CAE中选择：File -> Run Script
   - 选择 extract_nodal_forces_final.py 文件
   - 等待脚本执行完成

3. 输出结果
   - Excel文件：*_SET_RESPON_forces.xlsx
     * Summary工作表：各节点最大力值汇总
     * All_Data工作表：所有节点的完整时间历程数据
     * Node_XXXXX工作表：受力最大的前10个节点的详细数据
   
   - 如果缺少pandas库，会自动生成CSV文件

重要说明：
=========

1. 真实节点力提取
   - 本脚本提取的是节点的真实等效合力，而不是Abaqus CAE显示的平均值
   - 基于CSDN专业文章的理论指导
   - 通过分别提取NFORC1、NFORC2、NFORC3并对每个节点求和实现

2. 方向修正
   - NFORC方向已自动修正（加负号）
   - 符合实际物理意义

3. 依赖库
   - 推荐安装：pandas, openpyxl, numpy
   - 如果没有安装，脚本会自动回退到CSV格式

故障排除：
=========

1. "未找到节点集 SET-RESPON"
   - 检查节点集名称是否正确
   - 在Abaqus CAE中确认节点集是否存在

2. "未找到NFORC1/NFORC2/NFORC3字段"
   - 确保在Job设置中开启了节点力输出
   - 检查Field Output Requests中是否包含NFORC1、NFORC2、NFORC3

3. "无法获取odb文件"
   - 确保在Abaqus CAE中已经打开了odb文件

4. Excel文件生成失败
   - 安装pandas和openpyxl库：pip install pandas openpyxl numpy
   - 脚本会自动回退到CSV格式

输出数据格式：
=============

CSV/Excel数据格式：
NodeID,Time,Fx,Fy,Fz,F_Total
108700,0.000000,1.234e-03,-2.567e-04,3.890e-05,1.267e-03
108700,0.001000,1.456e-03,-2.789e-04,4.123e-05,1.489e-03
...

其中：
- NodeID：节点编号
- Time：时间值
- Fx, Fy, Fz：三个方向的节点力（已修正方向）
- F_Total：合力大小

技术支持：
=========

如有问题，请参考：
- CSDN文章：https://blog.csdn.net/qianxin1234/article/details/140739068
- Abaqus Documentation: NFORC Field Output
