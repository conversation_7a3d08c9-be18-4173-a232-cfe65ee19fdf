# -*- coding: utf-8 -*-
from abaqus import *
from abaqusConstants import *
import pandas as pd

MODEL_NAME = '33-4'
INSTANCE_NAME = 'PART-1-1'
EXCEL_FILE = r'C:\Users\<USER>\Desktop\abaqus-set\mapping_export.xlsx'
SET_NAME = 'Set-RESPON'

# 读取Excel文件中的节点ID，排除最后三行
df = pd.read_excel(EXCEL_FILE)
node_ids = df['Abaqus Node ID'].dropna().astype(int).iloc[:-1].tolist()

print(f"从Excel文件读取到 {len(node_ids)} 个节点ID")

a = mdb.models[MODEL_NAME].rootAssembly

# 创建包含所有节点的集合
a.SetFromNodeLabels(name=SET_NAME, nodeLabels=((INSTANCE_NAME, tuple(node_ids)),))

print(f"已创建包含 {len(node_ids)} 个节点的集合: {SET_NAME}")
print("请检查Abaqus消息区和模型树，确认集合是否成功创建。")
