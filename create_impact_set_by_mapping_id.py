# -*- coding: utf-8 -*-
"""
基于Custom Mapping ID创建冲击点Node Set脚本
手动输入映射ID，自动读取对应的Abaqus Node ID创建node set
"""
from abaqus import *
from abaqusConstants import *
import pandas as pd

# =============================================================================
# 配置参数 - 请根据需要修改
# =============================================================================
MODEL_NAME = '33-4'
INSTANCE_NAME = 'PART-1-1'
EXCEL_FILE = r'C:\Users\<USER>\Desktop\abaqus-set\mapping_export.xlsx'

# 在这里输入您要创建node set的映射ID
MAPPING_ID = 'TB-1'  # 请修改为您需要的映射ID

# 创建的set名称（可选自定义）
SET_NAME = f'Set-Target-{MAPPING_ID}'  # 自动根据映射ID命名
# SET_NAME = 'Set-IMPACT-CUSTOM'  # 或者使用自定义名称

# =============================================================================
# 主程序
# =============================================================================

print(f"开始为映射ID '{MAPPING_ID}' 创建Node Set...")

try:
    # 读取Excel文件
    df = pd.read_excel(EXCEL_FILE)
    print(f"成功读取Excel文件，共 {len(df)} 行数据")
    
    # 根据Custom Mapping ID筛选数据
    filtered_df = df[df['Custom Mapping ID'] == MAPPING_ID]
    
    if filtered_df.empty:
        print(f"错误: 未找到映射ID '{MAPPING_ID}' 对应的数据")
        print("\n可用的映射ID示例:")
        available_ids = df['Custom Mapping ID'].dropna().unique()[:10]
        for aid in available_ids:
            print(f"  - {aid}")
        if len(df['Custom Mapping ID'].dropna().unique()) > 10:
            print(f"  ... 还有更多映射ID")
    else:
        # 获取对应的Abaqus Node ID
        node_ids = filtered_df['Abaqus Node ID'].dropna().astype(int).tolist()
        print(f"找到 {len(node_ids)} 个节点ID")
        
        if node_ids:
            # 创建node set
            a = mdb.models[MODEL_NAME].rootAssembly
            a.SetFromNodeLabels(name=SET_NAME, nodeLabels=((INSTANCE_NAME, tuple(node_ids)),))
            
            print(f"✓ 成功创建Node Set: {SET_NAME}")
            print(f"  映射ID: {MAPPING_ID}")
            print(f"  包含节点数量: {len(node_ids)}")
            print("  请检查Abaqus模型树确认创建成功")
        else:
            print(f"错误: 映射ID '{MAPPING_ID}' 没有有效的节点ID数据")

except Exception as e:
    print(f"执行过程中出现错误: {e}")
    print("\n请检查:")
    print("1. Excel文件路径是否正确")
    print("2. 模型名称和实例名称是否正确") 
    print("3. 映射ID是否存在于Excel文件的'Custom Mapping ID'列中")

print("\n脚本执行完成")
