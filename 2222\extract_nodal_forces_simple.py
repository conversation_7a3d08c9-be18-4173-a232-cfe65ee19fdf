# -*- coding: utf-8 -*-
"""
Abaqus Node Force Extraction Tool - Simple Version
Extract real nodal forces from SET-RESPON node set
Based on CSDN article: https://blog.csdn.net/qianxin1234/article/details/140739068

Usage:
1. Open Abaqus CAE
2. Open odb file with results
3. Run this script: File -> Run Script -> select this file
4. Excel file will be generated automatically

Dependencies: pandas, openpyxl, numpy (optional, will fallback to CSV)
"""

from abaqus import *
from abaqusConstants import *
from visualization import *
import os

# Try to import required libraries
try:
    import pandas as pd
    import numpy as np
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("Warning: pandas/numpy not installed, will use CSV format")

# Configuration
NODE_SET_NAME = "SET-RESPON"
OUTPUT_DIR = ""
ODB_PATH = ""  # Set your odb file path here, or leave empty to use current CAE session

def extract_nodal_forces():
    """Main function to extract nodal forces"""
    
    print("=" * 60)
    print("Abaqus Nodal Force Extraction Tool")
    print("=" * 60)
    
    # Get odb file
    if ODB_PATH:
        # Use specified odb file path
        try:
            from odbAccess import openOdb
            myodb = openOdb(path=ODB_PATH)
            print("Using specified odb file: " + str(ODB_PATH))
        except:
            print("Error: Cannot open odb file: " + str(ODB_PATH))
            return None, None
    else:
        # Try to use current CAE session
        try:
            viewport = session.viewports[session.currentViewportName]
            if hasattr(viewport, 'odbDisplay'):
                odb_name = viewport.odbDisplay.odbName
                myodb = session.odbs[odb_name]
                print("Using current odb file: " + str(odb_name))
            else:
                print("Error: Please open odb file in Abaqus CAE first or set ODB_PATH")
                return None, None
        except:
            print("Error: Cannot get current odb file. Please set ODB_PATH in script")
            return None, None
    
    # Check node set
    try:
        node_set = myodb.rootAssembly.nodeSets[NODE_SET_NAME]
        print("Found node set: " + str(NODE_SET_NAME))
        
        # Get node labels
        node_labels = []
        for instance_nodes in node_set.nodes:
            for node in instance_nodes:
                node_labels.append(node.label)
        
        print("Number of nodes: " + str(len(node_labels)))
        print("Node ID range: " + str(min(node_labels)) + " - " + str(max(node_labels)))
        
    except KeyError:
        print("Error: Node set " + str(NODE_SET_NAME) + " not found")
        print("Available node sets:")
        for set_name in myodb.rootAssembly.nodeSets.keys():
            print("  - " + str(set_name))
        return None, None
    
    # Get all steps
    step_names = myodb.steps.keys()
    print("Processing steps: " + ', '.join(step_names))
    
    # Store all data
    all_data = {}
    
    # Initialize data storage for each node
    for node_id in node_labels:
        all_data[node_id] = {
            'Time': [],
            'Fx': [],
            'Fy': [],
            'Fz': []
        }
    
    # Process all steps and frames
    total_frames = 0
    for step_name in step_names:
        step = myodb.steps[step_name]
        print("Processing step " + str(step_name) + ", frames: " + str(len(step.frames)))
        
        for frame_idx, frame in enumerate(step.frames):
            try:
                time_val = frame.frameValue
                
                # Check NFORC fields
                if 'NFORC1' not in frame.fieldOutputs or 'NFORC2' not in frame.fieldOutputs or 'NFORC3' not in frame.fieldOutputs:
                    print("Warning: NFORC1/NFORC2/NFORC3 fields not found in frame " + str(frame_idx))
                    continue
                
                # Get NFORC fields
                nforc1_field = frame.fieldOutputs['NFORC1']
                nforc2_field = frame.fieldOutputs['NFORC2'] 
                nforc3_field = frame.fieldOutputs['NFORC3']
                
                # Extract NFORC values for SET-RESPON nodes
                subset1 = nforc1_field.getSubset(region=node_set)
                subset2 = nforc2_field.getSubset(region=node_set)
                subset3 = nforc3_field.getSubset(region=node_set)
                
                # Create current frame forces dictionary - sum all element contributions for each node
                current_frame_forces = {}
                
                # Process X direction force (NFORC1)
                for value in subset1.values:
                    node_label = value.nodeLabel
                    fx = -value.data  # Add negative sign to correct direction
                    if node_label not in current_frame_forces:
                        current_frame_forces[node_label] = [0.0, 0.0, 0.0]
                    current_frame_forces[node_label][0] += fx
                
                # Process Y direction force (NFORC2)
                for value in subset2.values:
                    node_label = value.nodeLabel
                    fy = -value.data  # Add negative sign to correct direction
                    if node_label not in current_frame_forces:
                        current_frame_forces[node_label] = [0.0, 0.0, 0.0]
                    current_frame_forces[node_label][1] += fy
                
                # Process Z direction force (NFORC3)
                for value in subset3.values:
                    node_label = value.nodeLabel
                    fz = -value.data  # Add negative sign to correct direction
                    if node_label not in current_frame_forces:
                        current_frame_forces[node_label] = [0.0, 0.0, 0.0]
                    current_frame_forces[node_label][2] += fz
                
                # Add data to all nodes
                for node_id in node_labels:
                    all_data[node_id]['Time'].append(time_val)
                    if node_id in current_frame_forces:
                        fx, fy, fz = current_frame_forces[node_id]
                        all_data[node_id]['Fx'].append(fx)
                        all_data[node_id]['Fy'].append(fy)
                        all_data[node_id]['Fz'].append(fz)
                    else:
                        # If node has no data in current frame, fill with zeros
                        all_data[node_id]['Fx'].append(0.0)
                        all_data[node_id]['Fy'].append(0.0)
                        all_data[node_id]['Fz'].append(0.0)
                
                total_frames += 1
                if frame_idx % 20 == 0:
                    print("  Progress: " + str(frame_idx + 1) + "/" + str(len(step.frames)) + " frames")
                
            except Exception as e:
                print("Error processing frame " + str(frame_idx) + ": " + str(e))
                continue
    
    print("Data extraction completed!")
    print("Total frames: " + str(total_frames))
    print("Total nodes: " + str(len(all_data)))
    
    # Close odb if opened by script
    if ODB_PATH:
        myodb.close()

    return all_data, myodb.name

def save_data_to_excel(all_data, odb_name):
    """Save data to Excel or CSV file"""

    # Determine output directory
    if OUTPUT_DIR:
        output_dir = OUTPUT_DIR
    else:
        script_path = os.path.abspath(__file__)
        output_dir = os.path.dirname(script_path)
        if not output_dir:
            output_dir = os.getcwd()

    # Create output file name
    base_name = os.path.splitext(os.path.basename(odb_name))[0]

    if HAS_PANDAS:
        # Generate Excel file
        excel_file = os.path.join(output_dir, base_name + "_SET_RESPON_forces.xlsx")

        try:
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # 1. Create summary statistics table
                summary_data = []
                for node_id in sorted(all_data.keys()):
                    node_data = all_data[node_id]
                    if node_data['Fx']:  # Ensure there is data
                        fx_array = np.array(node_data['Fx'])
                        fy_array = np.array(node_data['Fy'])
                        fz_array = np.array(node_data['Fz'])
                        f_total = np.sqrt(fx_array**2 + fy_array**2 + fz_array**2)

                        summary_data.append({
                            'NodeID': node_id,
                            'Max_Fx': np.max(np.abs(fx_array)),
                            'Max_Fy': np.max(np.abs(fy_array)),
                            'Max_Fz': np.max(np.abs(fz_array)),
                            'Max_F_Total': np.max(f_total),
                            'Mean_F_Total': np.mean(f_total),
                            'Time_at_Max_F': node_data['Time'][np.argmax(f_total)]
                        })

                summary_df = pd.DataFrame(summary_data)
                summary_df = summary_df.sort_values('Max_F_Total', ascending=False)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # 2. Create combined data table for all nodes
                all_records = []
                for node_id in sorted(all_data.keys()):
                    node_data = all_data[node_id]
                    for i in range(len(node_data['Time'])):
                        fx, fy, fz = node_data['Fx'][i], node_data['Fy'][i], node_data['Fz'][i]
                        f_total = (fx**2 + fy**2 + fz**2)**0.5
                        all_records.append({
                            'NodeID': node_id,
                            'Time': node_data['Time'][i],
                            'Fx': fx,
                            'Fy': fy,
                            'Fz': fz,
                            'F_Total': f_total
                        })

                all_df = pd.DataFrame(all_records)
                all_df.to_excel(writer, sheet_name='All_Data', index=False)

                # 3. Create separate sheets for key nodes (top 10 nodes with highest forces)
                top_nodes = summary_df.head(10)['NodeID'].tolist()
                for node_id in top_nodes:
                    node_data = all_data[node_id]
                    df = pd.DataFrame(node_data)
                    # Add total force column
                    df['F_Total'] = np.sqrt(df['Fx']**2 + df['Fy']**2 + df['Fz']**2)
                    sheet_name = "Node_" + str(node_id)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            print("Excel file saved to: " + str(excel_file))
            return excel_file, summary_df

        except Exception as e:
            print("Error saving Excel file: " + str(e))
            return save_data_to_csv(all_data, base_name, output_dir)

    else:
        # Fallback to CSV format
        return save_data_to_csv(all_data, base_name, output_dir)

def save_data_to_csv(all_data, base_name, output_dir):
    """Save data to CSV file"""

    csv_file = os.path.join(output_dir, base_name + "_SET_RESPON_forces.csv")

    try:
        with open(csv_file, 'w') as f:
            # Write header
            f.write("NodeID,Time,Fx,Fy,Fz,F_Total\n")

            # Write data
            for node_id in sorted(all_data.keys()):
                node_data = all_data[node_id]
                for i in range(len(node_data['Time'])):
                    fx, fy, fz = node_data['Fx'][i], node_data['Fy'][i], node_data['Fz'][i]
                    f_total = (fx**2 + fy**2 + fz**2)**0.5
                    f.write(str(node_id) + "," + str(node_data['Time'][i]) + "," +
                           str(fx) + "," + str(fy) + "," + str(fz) + "," + str(f_total) + "\n")

        print("CSV file saved to: " + str(csv_file))
        return csv_file, None

    except Exception as e:
        print("Error saving CSV file: " + str(e))
        return None, None

def main():
    """Main function"""

    # Extract nodal force data
    all_data, odb_name = extract_nodal_forces()
    if all_data is None:
        return

    # Save data to Excel/CSV
    output_file, summary_df = save_data_to_excel(all_data, odb_name)
    if output_file is None:
        print("Data saving failed")
        return

    # Print completion information
    print("\n" + "=" * 60)
    print("Nodal force extraction completed!")
    print("Output file: " + str(output_file))
    if summary_df is not None:
        print("Top 5 nodes with highest forces:")
        for i, (_, row) in enumerate(summary_df.head(5).iterrows(), 1):
            print("  " + str(i) + ". Node " + str(int(row['NodeID'])) +
                  ": Max total force = " + "{:.3e}".format(row['Max_F_Total']) + " N")
    print("=" * 60)

# Run main function
if __name__ == "__main__":
    main()
